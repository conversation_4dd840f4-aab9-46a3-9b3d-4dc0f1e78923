import pytest
from unittest.mock import patch, Mock
from fastapi.testclient import TestClient

class TestWorkflowRoutes:
    @pytest.fixture(autouse=True)
    def setup(self, test_client: TestClient, user_headers):
        self.client = test_client
        self.headers = user_headers

    @patch('app.services.workflow_service.WorkflowServiceClient')
    def test_create_workflow_success(self, mock_workflow_service):
        # Arrange
        mock_workflow_service.return_value.create_workflow.return_value = {
            "id": "workflow_123",
            "status": "created"
        }
        
        workflow_data = {
            "name": "Test Workflow",
            "description": "Test workflow description",
            "steps": [{"id": "step1", "type": "task"}]
        }

        # Act
        response = self.client.post(
            "/api/v1/workflows",
            json=workflow_data,
            headers=self.headers
        )

        # Assert
        assert response.status_code == 200
        assert "id" in response.json()

    @patch('app.services.workflow_service.WorkflowServiceClient')
    def test_get_workflow_status_success(self, mock_workflow_service):
        mock_workflow_service.return_value.get_workflow_status.return_value = {
            "id": "workflow_123",
            "status": "running"
        }
        
        response = self.client.get(
            "/api/v1/workflows/status/workflow_123",
            headers=self.headers
        )
        
        assert response.status_code == 200
        assert response.json()["status"] == "running"

    @patch('app.services.workflow_service.WorkflowServiceClient')
    def test_create_workflow_request_success(self, mock_workflow_service):
        workflow_data = {
            "workflow_id": "test_workflow",
            "payload": {
                "user_dependent_fields": ["field1", "field2"],
                "user_payload_template": {
                    "field1": "value1",
                    "field2": "value2"
                }
            }
        }
        
        response = self.client.post(
            "/api/v1/kafka/workflow-requests",
            json=workflow_data,
            headers=self.headers
        )
        
        assert response.status_code == 200

    @patch('app.services.workflow_service.WorkflowServiceClient')
    def test_list_workflow_versions(self, mock_workflow_service):
        mock_workflow_service.return_value.list_workflow_versions.return_value = Mock(
            success=True,
            message="Listed versions",
            versions=[Mock(id="v1", version_number="1.0.0", is_current=True)],
            total=1,
            page=1,
            total_pages=1,
            current_version_id="v1"
        )
        response = self.client.get(
            "/api/v1/workflows/workflow_123/versions",
            headers=self.headers
        )
        assert response.status_code == 200
        assert response.json()["success"] is True
        assert response.json()["versions"][0]["id"] == "v1"

    @patch('app.services.workflow_service.WorkflowServiceClient')
    def test_get_workflow_version(self, mock_workflow_service):
        mock_workflow_service.return_value.get_workflow_version.return_value = Mock(
            success=True,
            message="Got version",
            version=Mock(id="v1", version_number="1.0.0", is_current=True)
        )
        response = self.client.get(
            "/api/v1/workflows/workflow_123/versions/v1",
            headers=self.headers
        )
        assert response.status_code == 200
        assert response.json()["success"] is True
        assert response.json()["version"]["id"] == "v1"

    @patch('app.services.workflow_service.WorkflowServiceClient')
    def test_switch_workflow_version(self, mock_workflow_service):
        mock_workflow_service.return_value.switch_workflow_version.return_value = Mock(
            success=True,
            message="Switched version",
            new_current_version=Mock(id="v2", version_number="2.0.0", is_current=True)
        )
        response = self.client.post(
            "/api/v1/workflows/workflow_123/versions/v2/switch",
            headers=self.headers
        )
        assert response.status_code == 200
        assert response.json()["success"] is True
        assert response.json()["new_current_version"]["id"] == "v2"

    @patch('app.services.workflow_service.WorkflowServiceClient')
    def test_create_workflow_version(self, mock_workflow_service):
        mock_workflow_service.return_value.create_workflow_version.return_value = Mock(
            success=True,
            message="Created version",
            version=Mock(id="v3", version_number="3.0.0", is_current=False)
        )
        payload = {
            "workflow_id": "workflow_123",
            "user_id": "user_1",
            "name": "v3",
            "description": "desc",
            "changelog": "log",
            "auto_increment": True,
            "version_number": "3.0.0"
        }
        response = self.client.post(
            "/api/v1/workflows/workflow_123/versions",
            json=payload,
            headers=self.headers
        )
        assert response.status_code == 200
        assert response.json()["success"] is True
        assert response.json()["version"]["id"] == "v3"
