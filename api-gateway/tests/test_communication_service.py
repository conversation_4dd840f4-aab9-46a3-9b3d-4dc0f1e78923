import pytest
from unittest.mock import Mock, patch
import grpc
from app.services.communication_service import CommunicationServiceClient
from fastapi import HTTPException

@pytest.mark.asyncio
class TestCommunicationService:
    async def test_send_email_success(self, mock_communication_stub):
        # Arrange
        client = CommunicationServiceClient()
        mock_response = Mock()
        mock_response.message = "Email sent successfully"
        mock_communication_stub.return_value.SendEmail.return_value = mock_response

        # Act
        result = await client.send_email(
            recipient="<EMAIL>",
            subject="Test Subject",
            body="Test Body"
        )

        # Assert
        assert result == {"message": "Email sent successfully"}

    async def test_send_email_error(self, mock_communication_stub, grpc_error):
        # Arrange
        client = CommunicationServiceClient()
        mock_communication_stub.return_value.SendEmail.side_effect = grpc_error

        # Act & Assert
        with pytest.raises(HTTPException) as exc_info:
            await client.send_email(
                recipient="<EMAIL>",
                subject="Test Subject",
                body="Test Body"
            )
        assert exc_info.value.status_code == 500

    async def test_send_sms_success(self, mock_communication_stub):
        # Arrange
        client = CommunicationServiceClient()
        mock_response = Mock()
        mock_response.message = "SMS sent successfully"
        mock_communication_stub.return_value.SendSms.return_value = mock_response

        # Act
        result = await client.send_sms(
            phone_number="+1234567890",
            message="Test Message"
        )

        # Assert
        assert result == {"message": "SMS sent successfully"}

    async def test_send_sms_error(self, mock_communication_stub, grpc_error):
        # Arrange
        client = CommunicationServiceClient()
        mock_communication_stub.return_value.SendSms.side_effect = grpc_error

        # Act & Assert
        with pytest.raises(HTTPException) as exc_info:
            await client.send_sms(
                phone_number="+1234567890",
                message="Test Message"
            )
        assert exc_info.value.status_code == 500