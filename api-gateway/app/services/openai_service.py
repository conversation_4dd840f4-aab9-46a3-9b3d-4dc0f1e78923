import logging
from typing import Dict, Any, Optional

import openai
from openai import OpenAI

from app.core.config import settings

logger = logging.getLogger(__name__)


class OpenAIService:
    """Service for interacting with OpenAI API through Requesty router."""

    def __init__(self):
        """Initialize the OpenAI service with Requesty router configuration."""
        self.api_key = settings.REQUESTY_API_KEY
        self.model = settings.OPENAI_MODEL
        self.client = OpenAI(
            api_key=self.api_key,
            base_url=settings.REQUESTY_BASE_URL,
            default_headers={
                "HTTP-Referer": "API Gateway Service",  # Optional
                "X-Title": "RUH API Gateway",  # Optional
            }
        )

    def improve_system_prompt(
        self, original_prompt: str, agent_context: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Improve an agent's system prompt using OpenAI through Requesty.

        Args:
            original_prompt: The original system prompt to improve
            agent_context: Optional context about the agent (capabilities, purpose, etc.)

        Returns:
            str: The improved system prompt
        """
        try:
            if not self.api_key:
                logger.error("Requesty API key is not set")
                return original_prompt

            # Prepare context information
            context_str = ""
            if agent_context:
                context_str = "Agent context:\n"
                for key, value in agent_context.items():
                    context_str += f"- {key}: {value}\n"

            # Create the prompt for OpenAI
            prompt_improvement_prompt = f"""
            You are an expert AI prompt engineer. Your task is to improve the following system prompt for an AI agent.
            
            {context_str}
            
            ORIGINAL PROMPT:
            ```
            {original_prompt}
            ```
            
            Please improve this prompt to make it:
            1. More clear and specific
            2. Better at guiding the agent to perform its intended tasks
            3. Include appropriate constraints and guidelines
            4. Follow best practices for AI system prompts
            
            Return ONLY the improved prompt text without any explanations or additional text.
            """

            # Call OpenAI API through Requesty router
            response = self.client.chat.completions.create(
                model=f"openai/{self.model}",  # Using Requesty format with env model
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert AI prompt engineer specializing in creating effective system prompts for AI agents.",
                    },
                    {"role": "user", "content": prompt_improvement_prompt},
                ],
                temperature=0.7,
                max_tokens=4000,
            )

            # Extract and return the improved prompt
            improved_prompt = response.choices[0].message.content.strip()
            return improved_prompt

        except Exception as e:
            logger.error(f"Error improving system prompt with Requesty: {str(e)}")
            return original_prompt  # Return original prompt if there's an error

    def improve_user_prompt(self, original_prompt: str, mode: str = "ASK") -> str:
        """
        Improve a user's prompt to be more concise, clear, and comprehensive.

        Args:
            original_prompt: The original user prompt to improve
            mode: The mode of the prompt - "ACT" for task/action prompts, "ASK" for question/search prompts

        Returns:
            str: The improved user prompt
        """
        try:
            if not self.api_key:
                logger.error("Requesty API key is not set")
                return original_prompt

            # Create mode-specific improvement instructions
            if mode == "ACT":
                mode_instructions = """
                This is an ACTION/TASK prompt where the user wants an agent to perform specific tasks or actions.
                
                Focus on making the prompt:
                - Clear about what ACTION needs to be performed
                - Specific about the DELIVERABLES or OUTCOMES expected
                - Include any CONSTRAINTS or REQUIREMENTS
                - Specify the FORMAT of the output/result
                - Break down complex tasks into clear steps if needed
                - Include relevant CONTEXT for the task
                - Make it actionable and executable by an AI agent
                
                Examples of good ACT prompts:
                - "Create a detailed project plan for launching a mobile app, including timeline, milestones, and resource allocation"
                - "Write a comprehensive market analysis report for the electric vehicle industry, focusing on 2024 trends and competitor analysis"
                - "Generate a complete social media strategy for a B2B SaaS company, including content calendar and KPIs"
                """
            else:  # mode == "ASK"
                mode_instructions = """
                This is a QUESTION/SEARCH prompt where the user wants information, explanations, or answers.
                
                Focus on making the prompt:
                - Clear about what INFORMATION is being sought
                - Specific about the SCOPE and DEPTH of the answer needed
                - Include CONTEXT for better understanding
                - Specify the preferred FORMAT of the response
                - Include any specific ASPECTS or ANGLES to cover
                - Make it clear what level of detail is expected
                - Specify if examples, comparisons, or sources are needed
                
                Examples of good ASK prompts:
                - "Explain the key differences between microservices and monolithic architecture, including pros/cons and use cases"
                - "What are the current trends in renewable energy adoption globally, with specific focus on solar and wind power statistics"
                - "Compare the top 5 project management methodologies, highlighting their best use cases and implementation requirements"
                """

            # Create the prompt for improving user prompts
            user_prompt_improvement_prompt = f"""
            You are an expert prompt engineer specializing in optimizing user prompts for AI interactions.
            
            {mode_instructions}
            
            Your task is to improve the following {mode} prompt to make it:
            1. More concise and to the point
            2. Clearer in its intent and requirements
            3. Comprehensive, covering all necessary aspects
            4. Specific about expected output format
            5. Include relevant context where needed
            6. Remove ambiguity and vague language
            7. Optimized for the {mode} mode requirements listed above
            
            ORIGINAL USER PROMPT:
            ```
            {original_prompt}
            ```
            
            Guidelines for improvement:
            - Keep the core intent and requirements
            - Make it more specific and actionable for {mode} mode
            - Add structure if the request is complex
            - Include output format specifications if applicable
            - Remove unnecessary words while maintaining clarity
            - Ensure all important aspects are covered
            - Apply {mode}-specific best practices mentioned above
            
            Return ONLY the improved prompt text without any explanations, meta-commentary, or additional text.
            """

            # Call OpenAI API through Requesty router
            response = self.client.chat.completions.create(
                model=f"openai/{self.model}",  # Using Requesty format with env model
                messages=[
                    {
                        "role": "system",
                        "content": f"You are an expert prompt engineer who specializes in optimizing user prompts for better AI interactions. You focus on clarity, conciseness, and comprehensiveness, with expertise in both ACTION/TASK prompts and QUESTION/SEARCH prompts.",
                    },
                    {"role": "user", "content": user_prompt_improvement_prompt},
                ],
                temperature=0.3,  # Lower temperature for more consistent improvements
                max_tokens=3000,
            )

            # Extract and return the improved prompt
            improved_prompt = response.choices[0].message.content.strip()
            return improved_prompt

        except Exception as e:
            logger.error(f"Error improving user prompt with Requesty: {str(e)}")
            return original_prompt  # Return original prompt if there's an error
