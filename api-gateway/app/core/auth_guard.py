from typing import Dict, List, Optional, Any
from jose import jwt
import os
from fastapi import HTTPException, Security, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from functools import wraps
from fastapi import Depends

from app.utils.redis.redis_service import RedisService
from app.services.user_service import UserService<PERSON>lient
from app.core.config import settings


# Auth guard base class
class BaseAuthGuard:
    security = HTTPBearer()

    def __init__(self):

        self.redis_service = RedisService()
        self.user_service = UserServiceClient()

    async def __call__(
        self,
        credentials: HTTPAuthorizationCredentials = Depends(HTTPBearer()),
    ) -> Dict[str, Any]:
        if not credentials:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="JWT Bearer is missing"
            )

        if credentials.scheme != "Bearer":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="JWT Bearer is missing"
            )
        user = await self.validate_token(credentials.credentials)
        return user

    async def validate_token(self, token: str) -> Dict[str, Any]:
        try:

            # Get JWT secret from environment variable
            jwt_secret = settings.JWT_SECRET_KEY
            token_info = jwt.decode(token, jwt_secret, algorithms=[settings.JWT_ALGORITHM])
            # Check if session is available in Redis
            redis_data = await self.check_session_available_in_redis(token_info.get("email"))
            if redis_data.get("success"):
                user_dict = {
                    "user_id": token_info.get("user_id"),
                    "email": token_info.get("email"),
                    "role": token_info.get("role"),  # Add role to user_dict
                    "name": token_info.get("name"),
                    "organisation_id": token_info.get("organization_id")
                }

                # Merge user data with Redis data
                return {**user_dict, **redis_data}
            else:
                raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Unauthorized")

        except jwt.ExpiredSignatureError:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Expired token")
        except jwt.JWTError as e:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="Error decoding JWT"
            )
        except Exception as e:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Unauthorized")

    async def check_session_available_in_redis(self, email: str) -> Dict[str, Any]:
        try:
            # Build the Redis key for the user's token
            redis_login_token_hash_key = "_user_login_"
            env = settings.ENV
            key = f"{env}{redis_login_token_hash_key}{email}"
            session = self.redis_service.get_data_from_redis(key, "token")
            if not session:
                return {"success": False}

            return {
                "success": True,
            }
        except Exception as e:
            return {"success": False}


def role_required(allowed_roles: List[str]):
    async def check_role(current_user: Dict[str, Any] = Depends(BaseAuthGuard())):
        user_role = current_user.get("role")
        if not user_role or user_role not in allowed_roles:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="Insufficient permissions"
            )
        return current_user

    return check_role
