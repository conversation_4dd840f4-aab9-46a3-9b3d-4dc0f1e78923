#!/bin/bash

# Ensure we're in the project root directory
cd "$(dirname "$0")"

# Clean up previous test results
rm -rf htmlcov
rm -f .coverage

# Install dependencies
poetry install

# Run tests with coverage
poetry run pytest --cov=app --cov-report=term-missing --cov-report=html

# The coverage report will be available in htmlcov/index.html
echo "Coverage report generated in htmlcov/index.html"

# Check if coverage is below threshold
coverage_score=$(poetry run coverage report | grep "TOTAL" | awk '{print $4}' | sed 's/%//')
if (( $(echo "$coverage_score < 100" | bc -l) )); then
    echo "Coverage is below 100% ($coverage_score%)"
    exit 1
else
    echo "Coverage is 100%"
fi
