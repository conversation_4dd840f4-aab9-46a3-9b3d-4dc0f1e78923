# Copyright (C) Dnspython Contributors, see LICENSE for text of ISC license

# Copyright (C) 2015 Red Hat, Inc.
# Author: <PERSON><PERSON> <<EMAIL>>
#
# Permission to use, copy, modify, and distribute this software and its
# documentation for any purpose with or without fee is hereby granted,
# provided that the above copyright notice and this permission notice
# appear in all copies.
#
# THE SOFTWARE IS PROVIDED 'AS IS' AND RED HAT DISCLAIMS ALL WARRANTIES
# WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
# MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL NOMINUM BE LIABLE FOR
# ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
# WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
# ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT
# OF OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.

import dns.immutable
import dns.rdtypes.euibase


@dns.immutable.immutable
class EUI64(dns.rdtypes.euibase.EUIBase):
    """EUI64 record"""

    # see: rfc7043.txt

    byte_len = 8  # 0123456789abcdef (in hex)
    text_len = byte_len * 3 - 1  # 01-23-45-67-89-ab-cd-ef
