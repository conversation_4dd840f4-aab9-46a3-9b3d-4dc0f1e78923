.. This data file has been placed in the public domain.
.. Derived from the Unicode character mappings available from
   <http://www.w3.org/2003/entities/xml/>.
   Processed by unicode2rstsubs.py, part of Docutils:
   <https://docutils.sourceforge.io>.

.. |bdquo|  unicode:: U+0201E .. DOUBLE LOW-9 QUOTATION MARK
.. |circ|   unicode:: U+002C6 .. MODIFIER LETTER CIRCUMFLEX ACCENT
.. |Dagger| unicode:: U+02021 .. DOUBLE DAGGER
.. |dagger| unicode:: U+02020 .. DAGGER
.. |emsp|   unicode:: U+02003 .. EM SPACE
.. |ensp|   unicode:: U+02002 .. EN SPACE
.. |euro|   unicode:: U+020AC .. EURO SIGN
.. |gt|     unicode:: U+0003E .. GREATER-THAN SIGN
.. |ldquo|  unicode:: U+0201C .. LEFT DOUBLE QUOTATION MARK
.. |lrm|    unicode:: U+0200E .. LEFT-TO-RIGHT MARK
.. |lsaquo| unicode:: U+02039 .. SINGLE LEFT-POINTING ANGLE QUOTATION MARK
.. |lsquo|  unicode:: U+02018 .. LEFT SINGLE QUOTATION MARK
.. |lt|     unicode:: U+0003C .. LESS-THAN SIGN
.. |mdash|  unicode:: U+02014 .. EM DASH
.. |ndash|  unicode:: U+02013 .. EN DASH
.. |OElig|  unicode:: U+00152 .. LATIN CAPITAL LIGATURE OE
.. |oelig|  unicode:: U+00153 .. LATIN SMALL LIGATURE OE
.. |permil| unicode:: U+02030 .. PER MILLE SIGN
.. |quot|   unicode:: U+00022 .. QUOTATION MARK
.. |rdquo|  unicode:: U+0201D .. RIGHT DOUBLE QUOTATION MARK
.. |rlm|    unicode:: U+0200F .. RIGHT-TO-LEFT MARK
.. |rsaquo| unicode:: U+0203A .. SINGLE RIGHT-POINTING ANGLE QUOTATION MARK
.. |rsquo|  unicode:: U+02019 .. RIGHT SINGLE QUOTATION MARK
.. |sbquo|  unicode:: U+0201A .. SINGLE LOW-9 QUOTATION MARK
.. |Scaron| unicode:: U+00160 .. LATIN CAPITAL LETTER S WITH CARON
.. |scaron| unicode:: U+00161 .. LATIN SMALL LETTER S WITH CARON
.. |thinsp| unicode:: U+02009 .. THIN SPACE
.. |tilde|  unicode:: U+002DC .. SMALL TILDE
.. |Yuml|   unicode:: U+00178 .. LATIN CAPITAL LETTER Y WITH DIAERESIS
.. |zwj|    unicode:: U+0200D .. ZERO WIDTH JOINER
.. |zwnj|   unicode:: U+0200C .. ZERO WIDTH NON-JOINER
