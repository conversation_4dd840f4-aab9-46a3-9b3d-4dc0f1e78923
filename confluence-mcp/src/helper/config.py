import os

from dotenv import load_dotenv

load_dotenv()

CONFLUENCE_URL = os.getenv("CONFLUENCE_URL")
CONFLUENCE_EMAIL = os.getenv("CONFLUENCE_EMAIL")
CONFLUENCE_API_TOKEN = os.getenv("CONFLUENCE_API_TOKEN")

if not CONFLUENCE_URL:
    CONFLUENCE_URL = input("Enter Confluence Base URL: ")
if not CONFLUENCE_EMAIL:
    CONFLUENCE_EMAIL = input("Enter Confluence email: ")
if not CONFLUENCE_API_TOKEN:
    CONFLUENCE_API_TOKEN = input("Enter Confluence API Token: ")


from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """Loads all configuration from environment variables or a .env file."""

    model_config = SettingsConfigDict(
        env_file=".env", env_file_encoding="utf-8", extra="ignore"
    )

    url: str = CONFLUENCE_URL
    email: str = CONFLUENCE_EMAIL
    token: str = CONFLUENCE_API_TOKEN


def load_settings() -> Settings:
    """Loads and returns the application settings."""
    return Settings()
