import json
from typing import Annotated, Any, Dict, List, Optional

from fastmcp import <PERSON><PERSON><PERSON>
from pydantic import Field

from .dependencies import get_confluence_client

# Create the FastMCP instance
mcp = FastMCP(name="Confluence MCP Service")


# --- PAGE TOOLS ---


@mcp.tool
async def get_page_by_id(
    page_id: Annotated[
        str, Field(description="The numeric ID of the Confluence page to fetch.")
    ],
    version: Annotated[
        Optional[int],
        Field(
            description="Optional. The specific version number of the page to retrieve.",
            default=None,
        ),
    ] = None,
    get_draft: Annotated[
        bool,
        Field(
            description="Optional. Set to true to retrieve the draft version of the page if it exists.",
            default=False,
        ),
    ] = False,
) -> str:
    """
    Get the full content and details of a specific Confluence page by its ID.
    Can optionally fetch a specific version or the draft version.
    """
    client = get_confluence_client()
    page_data = await client.get_page(page_id, version=version, get_draft=get_draft)
    return json.dumps(page_data, indent=2)


@mcp.tool
async def create_page(
    space_key: Annotated[
        Optional[str],
        Field(
            description="Key of the Confluence space (e.g., 'DEV'). Optional if space_id is provided.",
            default=None,
        ),
    ] = None,
    space_id: Annotated[
        Optional[int],
        Field(
            description="Numeric ID of the Confluence space. Optional if space_key is provided.",
            default=None,
        ),
    ] = None,
    title: Annotated[
        str,
        Field(
            description="Title of the new page. Must be unique under the parent page or root."
        ),
    ] = "",
    content: Annotated[
        str,
        Field(
            description="Body content of the page in Atlassian Storage Format (HTML-like markup)."
        ),
    ] = "",
    parent_id: Annotated[
        Optional[str],
        Field(
            description="Optional parent page ID under which this new page will be nested.",
            default=None,
        ),
    ] = None,
) -> Dict[str, Any]:
    """
    Create a new Confluence page in a specified space.

    You must provide either `space_key` or `space_id`. If both are provided, `space_id` takes priority.

    Returns details of the newly created page.
    """
    client = get_confluence_client()

    if not space_key and not space_id:
        raise ValueError(
            "Either 'space_key' or 'space_id' must be provided to create a page."
        )

    if not space_id and space_key:
        space_data = await client.get_space_by_key(space_key)
        space_id = space_data.get("id")
        if not space_id:
            raise ValueError(
                f"Could not resolve a numeric ID for space key '{space_key}'."
            )

    new_page = await client.create_page(space_id, title, content, parent_id)

    return {
        "message": "Page created successfully!",
        "id": new_page.get("id"),
        "title": new_page.get("title"),
        "status": new_page.get("status"),
        "url": new_page.get("_links", {}).get("webui"),
    }


# ------------------ SPACE TOOLS ------------------


@mcp.tool(tags=["space"])
async def get_spaces(
    keys: Annotated[
        Optional[List[str]],
        Field(description="Filter spaces by one or more space keys.", default=None),
    ] = None,
    labels: Annotated[
        Optional[List[str]],
        Field(description="Filter spaces by associated labels.", default=None),
    ] = None,
    status: Annotated[
        Optional[str],
        Field(
            description="Space status to filter by (e.g., 'current').",
            default="current",
        ),
    ] = "current",
    limit: Annotated[
        int,
        Field(
            description="Maximum number of spaces to return (1-100).",
            default=25,
            ge=1,
            le=100,
        ),
    ] = 25,
) -> List[Dict[str, Any]]:
    """
    Retrieve a list of Confluence spaces with basic metadata like ID, name, key, and status.
    """
    client = get_confluence_client()
    spaces_data = await client.get_spaces(
        keys=keys, labels=labels, status=status, limit=limit
    )

    return [
        {
            "id": s.get("id"),
            "key": s.get("key"),
            "name": s.get("name"),
            "status": s.get("status"),
        }
        for s in spaces_data.get("results", [])
    ]


@mcp.tool(tags=["space"])
async def get_space_details(
    space_key: Annotated[
        str,
        Field(
            description="Key of the Confluence space to fetch details for (e.g., 'DEV')."
        ),
    ],
) -> Dict[str, Any]:
    """
    Retrieve full details for a specific Confluence space by its key.
    """
    client = get_confluence_client()
    return await client.get_space_by_key(space_key)


@mcp.tool(tags=["space"])
async def create_space(
    name: Annotated[
        str, Field(description="Name of the new space to create. Must be unique.")
    ],
    key: Annotated[
        str,
        Field(
            description="Short uppercase identifier (no spaces) for the space (e.g., 'DEV')."
        ),
    ],
    description: Annotated[
        Optional[str],
        Field(
            description="Optional plain-text description for the space.", default=None
        ),
    ] = None,
) -> Dict[str, Any]:
    """
    Creates a new Confluence space. Returns a dictionary with details of the created space.
    """
    client = get_confluence_client()

    if not key:
        raise ValueError("Key must be provided.")

    if key and not key.isupper() or " " in key:
        raise ValueError("Space key must be all uppercase with no spaces.")

    new_space_data = await client.create_space(name, key, description)

    # FIX: Return the dictionary directly
    return {
        "message": "Space created successfully!",
        "id": new_space_data.get("id"),
        "key": new_space_data.get("key"),
        "name": new_space_data.get("name"),
    }


# @mcp.tool(tags=["space"])
# async def get_spaces(
#     keys: Annotated[
#         Optional[List[str]],
#         Field(
#             description="Optional. A list of space keys (e.g., ['TEST', 'DOCS']) to filter by.",
#             default=None,
#         ),
#     ] = None,
#     labels: Annotated[
#         Optional[List[str]],
#         Field(
#             description="Optional. A list of labels to filter spaces by.", default=None
#         ),
#     ] = None,
#     status: Annotated[
#         Optional[str],
#         Field(
#             description="Filter by space status. Valid: 'current', 'archived'.",
#             default="current",
#         ),
#     ] = "current",
#     limit: Annotated[
#         int,
#         Field(
#             description="Maximum number of spaces to return.", default=25, ge=1, le=100
#         ),
#     ] = 25,
# ) -> str:
#     """
#     Returns a list of Confluence spaces, with optional filters.
#     Example `keys`: ["TEST", "HR"]
#     Example `labels`: ["public", "documentation"]
#     """
#     client = get_confluence_client()
#     spaces_data = await client.get_spaces(
#         keys=keys, labels=labels, status=status, limit=limit
#     )
#     simplified_results = [
#         {
#             "id": s.get("id"),
#             "key": s.get("key"),
#             "name": s.get("name"),
#             "status": s.get("status"),
#         }
#         for s in spaces_data.get("results", [])
#     ]
#     return json.dumps(simplified_results, indent=2)


# @mcp.tool(tags=["space"])
# async def get_space_details(
#     space_key: Annotated[
#         str,
#         Field(
#             description="The key of the space to retrieve details for (e.g., 'TEST')."
#         ),
#     ],
# ) -> str:
#     """
#     Returns detailed information for a single space by its key.
#     This is useful for finding the numeric ID of a space.
#     """
#     client = get_confluence_client()
#     space_data = await client.get_space_by_key(space_key)

#     return json.dumps(
#         json.loads(space_data.text), sort_keys=True, indent=4, separators=(",", ": ")
#     )
#     return json.dumps(
#         json.loads(space_data.text), sort_keys=True, indent=4, separators=(",", ": ")
#     )
#     return json.dumps(space_data, indent=2)


# @mcp.tool(tags=["space"])
# async def create_space(
#     name: Annotated[
#         str, Field(description="The full name of the new space to be created.")
#     ],
#     key: Annotated[
#         str,
#         Field(
#             description="The unique key for the new space (e.g., 'NEWPROJ'). Must be all-caps with no spaces."
#         ),
#     ],
#     description: Annotated[
#         Optional[str],
#         Field(
#             description="Optional. A plain text description for the new space.",
#             default=None,
#         ),
#     ] = None,
# ) -> str:
#     """
#     Creates a new Confluence space. Requires global 'Create Space' permissions.
#     """
#     client = get_confluence_client()
#     if not key.isupper() or " " in key:
#         raise ValueError("Space key must be all uppercase with no spaces.")

#     new_space_data = await client.create_space(name, key, description)
#     response = {
#         "message": "Space created successfully!",
#         "id": new_space_data.get("id"),
#         "key": new_space_data.get("key"),
#         "name": new_space_data.get("name"),
#     }
#     return json.dumps(
#         json.loads(response.text), sort_keys=True, indent=4, separators=(",", ": ")
#     )
#     return json.dumps(response, indent=2)
