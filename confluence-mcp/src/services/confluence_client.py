from typing import List, Optional

import httpx
from src.helper.config import Settings


class ConfluenceV2Client:
    """A client for interacting with the Confluence v2 API using an API Token."""

    def __init__(self, settings: Settings):
        print("ettings.url: ", settings.url)
        self.base_url = f"{settings.url}/wiki"
        self.auth = httpx.BasicAuth(username=settings.email, password=settings.token)
        self.client = httpx.AsyncClient(
            base_url=self.base_url, auth=self.auth, timeout=30.0
        )
        print("✅ ConfluenceV2Client initialized.")

    async def get_page(
        self, page_id: str, version: int = None, get_draft: bool = False
    ) -> dict:
        print(f"CLIENT: Fetching page {page_id}...")
        params = {"body-format": "storage"}
        if version:
            params["version"] = version
        if get_draft:
            params["get-draft"] = "true"
        response = await self.client.get(f"/api/v2/pages/{page_id}", params=params)
        response.raise_for_status()
        return response.json()

    async def create_page(
        self, space_id: str, title: str, content: str, parent_id: str = None
    ) -> dict:
        print(f"CLIENT: Creating page '{title}' in space ID {space_id}...")
        escaped_content = content.replace("&", "&").replace("<", "<").replace(">", ">")
        payload = {
            "spaceId": space_id,
            "status": "current",
            "title": title,
            "body": {"representation": "storage", "value": f"<p>{escaped_content}</p>"},
        }
        if parent_id:
            payload["parentId"] = parent_id
        response = await self.client.post("/api/v2/pages", json=payload)
        response.raise_for_status()
        return response.json()

    async def get_spaces(
        self,
        keys: Optional[List[str]] = None,
        labels: Optional[List[str]] = None,
        space_type: Optional[str] = None,
        status: Optional[str] = None,
        sort: Optional[str] = None,
        limit: int = 25,
    ) -> dict:
        print(f"CLIENT: Fetching spaces with filters: keys={keys}, labels={labels}...")
        params = {"limit": limit}
        if keys:
            params["keys"] = ",".join(keys)
        if labels:
            params["labels"] = ",".join(labels)
        if space_type:
            params["type"] = space_type
        if status:
            params["status"] = status
        if sort:
            params["sort"] = sort
        response = await self.client.get("/api/v2/spaces", params=params)
        response.raise_for_status()
        return response.json()

    async def get_space_by_id(self, space_id: int) -> dict:
        print(f"CLIENT: Fetching space with ID {space_id}...")
        response = await self.client.get(f"/api/v2/spaces/{space_id}")
        response.raise_for_status()
        return response.json()

    async def create_space(
        self,
        name: str,
        key: str,
        description: Optional[str] = None,
        role_assignments: Optional[List[dict]] = None,
    ) -> dict:
        """
        Creates a new Confluence space using the v2 API.
        Either `key` or `alias` must be provided.

        Docs: https://developer.atlassian.com/cloud/confluence/rest/v2/api-group-space/#api-spaces-post
        """

        if not key:
            raise ValueError("The 'key' must be provided.")

        payload = {
            "name": name,
        }

        if key:
            payload["key"] = key
        if description:
            payload["description"] = {"value": description, "representation": "plain"}
        if role_assignments:
            payload["roleAssignments"] = role_assignments
        print("\n\n")
        print("******payload: ********", payload)
        print("\n\n")
        response = await self.client.post("/api/v2/spaces", json=payload)
        response.raise_for_status()
        return response.json()

    # --- THIS IS THE MISSING METHOD THAT NEEDS TO BE ADDED ---
    async def get_space_by_key(self, space_key: str) -> dict:
        """
        Fetches a single space by its key to find its details, including the numeric ID.
        """
        print(f"CLIENT: Looking up space with key '{space_key}'...")
        # We use the get_spaces method's `keys` filter to find the specific space
        response = await self.client.get("/api/v2/spaces", params={"keys": space_key})
        response.raise_for_status()
        results = response.json().get("results", [])
        if not results:
            raise ValueError(f"No space found with the key '{space_key}'.")
        # Return the first (and only) result found
        return results[0]

    async def update_page(
        self, page_id: str, new_title: str, new_content: str, new_status: str = "current"
    ) -> dict:
        """
        Updates an existing Confluence page.
        """
        print(f"CLIENT: Updating page {page_id}...")
        payload = {
            "status": new_status,
            "title": new_title,
            "body": {"representation": "storage", "value": f"<p>{new_content}</p>"},
        }
        response = await self.client.put(f"/api/v2/pages/{page_id}", json=payload)
        response.raise_for_status()
        return response.json()

    async def delete_page(self, page_id: str) -> None:
        """
        Deletes a Confluence page.
        """
        print(f"CLIENT: Deleting page {page_id}...")
        response = await self.client.delete(f"/api/v2/pages/{page_id}")
        response.raise_for_status()

    async def search_pages(self, query: str, limit: int = 25) -> dict:
        """
        Searches for Confluence pages.
        """
        print(f"CLIENT: Searching for pages with query: {query}...")
        params = {"query": query, "limit": limit}
        response = await self.client.get("/api/v2/pages", params=params)
        response.raise_for_status()
        return response.json()

    async def create_comment(self, page_id: str, content: str) -> dict:
        """
        Creates a new comment on a Confluence page.
        """
        print(f"CLIENT: Creating comment on page {page_id}...")
        payload = {
            "pageId": page_id,
            "body": {"representation": "storage", "value": f"<p>{content}</p>"},
        }
        response = await self.client.post("/api/v2/comments", json=payload)
        response.raise_for_status()
        return response.json()

    async def update_comment(self, comment_id: str, new_content: str) -> dict:
        """
        Updates an existing comment on a Confluence page.
        """
        print(f"CLIENT: Updating comment {comment_id}...")
        payload = {
            "body": {"representation": "storage", "value": f"<p>{new_content}</p>"},
        }
        response = await self.client.put(f"/api/v2/comments/{comment_id}", json=payload)
        response.raise_for_status()
        return response.json()

    async def delete_comment(self, comment_id: str) -> None:
        """
        Deletes a comment from a Confluence page.
        """
        print(f"CLIENT: Deleting comment {comment_id}...")
        response = await self.client.delete(f"/api/v2/comments/{comment_id}")
        response.raise_for_status()
