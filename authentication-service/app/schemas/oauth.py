"""
OAuth Pydantic Schemas

This module defines Pydantic models for OAuth API requests and responses
in the authentication service.
"""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from app.core.oauth_providers import OAuthProvider


class OAuthMetadata(BaseModel):
    """OAuth metadata passed via state parameter during OAuth flow."""

    user_id: str = Field(..., description="The ID of the user requesting authorization")
    tool_name: str = Field(..., description="The name of the tool (e.g., 'google_calendar')")
    provider: OAuthProvider = Field(..., description="The OAuth provider")
    scopes: List[str] = Field(default_factory=list, description="Requested OAuth scopes")
    timestamp: str = Field(..., description="Timestamp when the request was initiated")


class OAuthAuthorizeRequest(BaseModel):
    """Request model for initiating OAuth authorization."""

    user_id: str = Field(..., description="User ID requesting authorization")
    tool_name: str = Field(..., description="Tool name requiring authorization")
    provider: OAuthProvider = Field(..., description="OAuth provider to use")
    scopes: Optional[List[str]] = Field(
        default=None,
        description="Custom scopes (if not provided, tool default scopes will be used)",
    )
    redirect_uri: Optional[str] = Field(
        default=None,
        description="Custom redirect URI (if not provided, provider default will be used)",
    )


class OAuthAuthorizeResponse(BaseModel):
    """Response model for OAuth authorization initiation."""

    success: bool = Field(..., example=True)
    message: str = Field(..., example="OAuth authorization URL generated successfully")
    authorization_url: str = Field(..., example="https://accounts.google.com/o/oauth2/auth?...")
    state: str = Field(..., example="secure_state_token_123")
    provider: str = Field(..., example="google")
    expires_in: int = Field(..., example=900, description="State token expiry in seconds")


class OAuthCallbackRequest(BaseModel):
    """Request model for OAuth callback handling."""

    code: Optional[str] = Field(None, description="Authorization code from OAuth provider")
    state: str = Field(..., description="State parameter from OAuth provider")
    error: Optional[str] = Field(None, description="Error code if authorization failed")
    error_description: Optional[str] = Field(None, description="Error description")


class OAuthCallbackResponse(BaseModel):
    """Response model for OAuth callback handling."""

    success: bool = Field(..., example=True)
    message: str = Field(..., example="OAuth authorization completed successfully")
    user_id: Optional[str] = Field(None, example="user_123")
    tool_name: Optional[str] = Field(None, example="google_calendar")
    provider: Optional[str] = Field(None, example="google")


class OAuthCredentialRequest(BaseModel):
    """Request model for retrieving OAuth credentials."""

    user_id: str = Field(..., description="User ID")
    tool_name: str = Field(..., description="Tool name")
    provider: OAuthProvider = Field(default=OAuthProvider.GOOGLE, description="OAuth provider")


class OAuthCredentialResponse(BaseModel):
    """Response model for OAuth credential retrieval."""

    success: bool = Field(..., example=True)
    message: str = Field(..., example="OAuth credentials retrieved successfully")
    user_id: str = Field(..., example="user_123")
    tool_name: str = Field(..., example="google_calendar")
    provider: str = Field(..., example="google")
    access_token: str = Field(..., example="ya29.a0AfH6SMB...")
    refresh_token: Optional[str] = Field(None, example="1//0eXxyz...")
    token_type: str = Field(default="Bearer", example="Bearer")
    expires_in: int = Field(..., example=3599, description="Token expiry in seconds")
    scope: str = Field(..., example="https://www.googleapis.com/auth/calendar")
    
    # Slack-specific fields for dual-token support
    bot_token: Optional[str] = Field(None, example="xoxb-**********-**********123-abcdefghijklmnopqrstuvwx", description="Slack bot token")
    user_token: Optional[str] = Field(None, example="xoxp-**********-**********123-**********123-abcdefghijklmnopqrstuvwxyz123456", description="Slack user token")
    bot_user_id: Optional[str] = Field(None, example="U0123456789", description="Slack bot user ID")
    user_id_slack: Optional[str] = Field(None, example="U**********", description="Slack user ID")
    user_scope: Optional[str] = Field(None, example="search:read,users.profile:write", description="Slack user scopes")
    team_id: Optional[str] = Field(None, example="T**********", description="Slack team/workspace ID")
    team_name: Optional[str] = Field(None, example="My Workspace", description="Slack team/workspace name")


class ServerOAuthCredentialRequest(BaseModel):
    """Request model for server-to-server OAuth credential access."""

    server_auth_key: str = Field(..., description="Server authentication key")
    user_id: str = Field(..., description="User ID")
    tool_name: str = Field(..., description="Tool name")
    provider: OAuthProvider = Field(default=OAuthProvider.GOOGLE, description="OAuth provider")


class OAuthProviderInfo(BaseModel):
    """Information about an OAuth provider."""

    provider: str = Field(..., example="google")
    name: str = Field(..., example="Google")
    display_name: str = Field(..., example="Google OAuth")
    supported_tools: List[str] = Field(..., example=["google_calendar", "google_drive", "gmail"])
    is_configured: bool = Field(..., example=True)
    auth_url: str = Field(..., example="https://accounts.google.com/o/oauth2/auth")
    scopes_available: List[str] = Field(default_factory=list)


class OAuthProvidersListResponse(BaseModel):
    """Response model for listing OAuth providers."""

    success: bool = Field(..., example=True)
    message: str = Field(..., example="OAuth providers retrieved successfully")
    providers: List[OAuthProviderInfo] = Field(..., description="List of available providers")
    total_count: int = Field(..., example=3)


class OAuthToolScopesRequest(BaseModel):
    """Request model for getting tool scopes."""

    tool_name: str = Field(..., description="Tool name")
    provider: OAuthProvider = Field(default=OAuthProvider.GOOGLE, description="OAuth provider")


class OAuthToolScopesResponse(BaseModel):
    """Response model for tool scopes."""

    success: bool = Field(..., example=True)
    message: str = Field(..., example="Tool scopes retrieved successfully")
    tool_name: str = Field(..., example="google_calendar")
    provider: str = Field(..., example="google")
    scopes: List[str] = Field(..., example=["https://www.googleapis.com/auth/calendar"])
    description: Optional[str] = Field(None, example="Google Calendar access for event management")


class DeleteOAuthCredentialRequest(BaseModel):
    """Request model for deleting OAuth credentials."""

    user_id: str = Field(..., description="User ID")
    tool_name: str = Field(..., description="Tool name")
    provider: OAuthProvider = Field(default=OAuthProvider.GOOGLE, description="OAuth provider")


class DeleteOAuthCredentialResponse(BaseModel):
    """Response model for OAuth credential deletion."""

    success: bool = Field(..., example=True)
    message: str = Field(..., example="OAuth credentials deleted successfully")


class OAuthErrorResponse(BaseModel):
    """Generic error response model for OAuth operations."""

    success: bool = Field(default=False, example=False)
    message: str = Field(..., example="Authorization failed: access_denied")
    error_code: Optional[str] = Field(None, example="invalid_request")
    error_details: Optional[Dict[str, Any]] = Field(None)


class HealthCheckResponse(BaseModel):
    """Health check response model."""

    healthy: bool = Field(..., example=True)
    status: str = Field(..., example="healthy")
    version: str = Field(..., example="1.0.0")
    dependencies: Dict[str, str] = Field(
        default_factory=dict,
        example={"database": "healthy", "redis": "healthy", "secret_manager": "healthy"},
    )
    timestamp: str = Field(..., description="Health check timestamp")
