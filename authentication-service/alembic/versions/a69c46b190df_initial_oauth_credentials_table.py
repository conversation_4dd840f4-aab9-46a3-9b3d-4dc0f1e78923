"""Initial OAuth credentials table

Revision ID: a69c46b190df
Revises:
Create Date: 2025-06-11 16:48:26.451707

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'a69c46b190df'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Create oauth_credentials table
    op.create_table(
        'oauth_credentials',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('user_id', sa.String(), nullable=False),
        sa.Column('mcp_id', sa.String(), nullable=False),
        sa.Column('tool_name', sa.String(), nullable=False),
        sa.Column('provider', sa.String(), nullable=False, server_default='google'),
        sa.Column('composite_key', sa.String(), nullable=False),
        sa.Column('secret_reference', sa.String(), nullable=False),
        sa.Column('scopes', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
        sa.Column('last_used_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('composite_key')
    )

    # Create indexes
    op.create_index('idx_oauth_composite_key', 'oauth_credentials', ['composite_key'])
    op.create_index('idx_oauth_user_provider', 'oauth_credentials', ['user_id', 'provider'])
    op.create_index('idx_oauth_provider_tool', 'oauth_credentials', ['provider', 'tool_name'])
    op.create_index('idx_oauth_user_mcp_tool', 'oauth_credentials', ['user_id', 'mcp_id', 'tool_name'])
    op.create_index('idx_oauth_last_used', 'oauth_credentials', ['last_used_at'])
    op.create_index('ix_oauth_credentials_user_id', 'oauth_credentials', ['user_id'])
    op.create_index('ix_oauth_credentials_mcp_id', 'oauth_credentials', ['mcp_id'])
    op.create_index('ix_oauth_credentials_tool_name', 'oauth_credentials', ['tool_name'])
    op.create_index('ix_oauth_credentials_provider', 'oauth_credentials', ['provider'])


def downgrade() -> None:
    """Downgrade schema."""
    # Drop indexes
    op.drop_index('ix_oauth_credentials_provider', table_name='oauth_credentials')
    op.drop_index('ix_oauth_credentials_tool_name', table_name='oauth_credentials')
    op.drop_index('ix_oauth_credentials_mcp_id', table_name='oauth_credentials')
    op.drop_index('ix_oauth_credentials_user_id', table_name='oauth_credentials')
    op.drop_index('idx_oauth_last_used', table_name='oauth_credentials')
    op.drop_index('idx_oauth_user_mcp_tool', table_name='oauth_credentials')
    op.drop_index('idx_oauth_provider_tool', table_name='oauth_credentials')
    op.drop_index('idx_oauth_user_provider', table_name='oauth_credentials')
    op.drop_index('idx_oauth_composite_key', table_name='oauth_credentials')

    # Drop table
    op.drop_table('oauth_credentials')
