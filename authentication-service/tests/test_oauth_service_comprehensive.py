"""
Comprehensive OAuth Service Tests

This module contains comprehensive tests for the OAuth service
following TDD principles.
"""

import pytest
import json
from unittest.mock import Mock, patch, AsyncMock
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from datetime import datetime

from app.services.oauth_service import OAuthService
from app.core.oauth_providers import OAuthProvider
from app.models.oauth import OAuthCredential, Base
from app.core.config import settings


@pytest.fixture
def test_db():
    """Create test database."""
    engine = create_engine("sqlite:///:memory:")
    Base.metadata.create_all(engine)
    TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    return TestingSessionLocal()


@pytest.fixture
def oauth_service():
    """Create OAuth service instance."""
    return OAuthService()


@pytest.fixture
def mock_redis_service():
    """Mock Redis service."""
    with patch('app.services.oauth_service.redis_service') as mock:
        mock.set_data_in_redis_with_ttl.return_value = True
        mock.get_json_data_from_redis.return_value = {
            "user_id": "test_user",
            "mcp_id": "test_mcp",
            "tool_name": "google_calendar",
            "provider": "google",
            "scopes": ["https://www.googleapis.com/auth/calendar"],
            "timestamp": datetime.utcnow().isoformat()
        }
        mock.delete_data_from_redis.return_value = True
        yield mock


@pytest.fixture
def mock_secret_manager():
    """Mock Secret Manager."""
    with patch('app.services.oauth_service.secret_manager') as mock:
        mock.store_oauth_tokens.return_value = "test_secret_id"
        mock.retrieve_oauth_tokens.return_value = {
            "access_token": "test_access_token",
            "refresh_token": "test_refresh_token",
            "token_type": "Bearer",
            "expires_in": 3600
        }
        mock.delete_oauth_tokens.return_value = True
        yield mock


class TestOAuthService:
    """Test cases for OAuth service."""
    
    def test_generate_authorization_url_google(self, oauth_service, mock_redis_service):
        """Test generating Google OAuth authorization URL."""
        # Test data
        user_id = "test_user"
        mcp_id = "test_mcp"
        tool_name = "google_calendar"
        provider = OAuthProvider.GOOGLE
        
        # Mock provider configuration
        with patch('app.services.oauth_service.oauth_provider_manager') as mock_manager:
            mock_config = Mock()
            mock_config.client_id = "test_client_id"
            mock_config.redirect_uri = "http://localhost:8000/callback"
            mock_config.auth_url = "https://accounts.google.com/o/oauth2/auth"
            mock_config.access_type = "offline"
            mock_config.prompt = "consent"
            mock_config.response_type = "code"
            mock_config.extra_auth_params = {}
            
            mock_manager.get_provider_config.return_value = mock_config
            mock_manager.is_provider_configured.return_value = True
            mock_manager.get_tool_scopes.return_value = ["https://www.googleapis.com/auth/calendar"]
            
            # Generate authorization URL
            auth_url, state_token = oauth_service.generate_authorization_url(
                user_id, mcp_id, tool_name, provider
            )
            
            # Assertions
            assert auth_url is not None
            assert state_token is not None
            assert "accounts.google.com" in auth_url
            assert "client_id=test_client_id" in auth_url
            assert f"state={state_token}" in auth_url
            assert "scope=" in auth_url
            
            # Verify Redis was called to store state
            mock_redis_service.set_data_in_redis_with_ttl.assert_called_once()
    
    def test_generate_authorization_url_invalid_provider(self, oauth_service):
        """Test generating authorization URL with invalid provider."""
        with patch('app.services.oauth_service.oauth_provider_manager') as mock_manager:
            mock_manager.get_provider_config.return_value = None
            
            with pytest.raises(ValueError, match="Provider not configured"):
                oauth_service.generate_authorization_url(
                    "user", "mcp", "tool", OAuthProvider.GOOGLE
                )
    
    def test_generate_authorization_url_no_scopes(self, oauth_service):
        """Test generating authorization URL with no scopes configured."""
        with patch('app.services.oauth_service.oauth_provider_manager') as mock_manager:
            mock_config = Mock()
            mock_manager.get_provider_config.return_value = mock_config
            mock_manager.is_provider_configured.return_value = True
            mock_manager.get_tool_scopes.return_value = []
            
            with pytest.raises(ValueError, match="No scopes configured"):
                oauth_service.generate_authorization_url(
                    "user", "mcp", "tool", OAuthProvider.GOOGLE
                )
    
    @pytest.mark.asyncio
    async def test_exchange_code_for_tokens_success(self, oauth_service, mock_redis_service):
        """Test successful token exchange."""
        code = "test_auth_code"
        state = "test_state_token"
        
        # Mock HTTP response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "access_token": "test_access_token",
            "refresh_token": "test_refresh_token",
            "token_type": "Bearer",
            "expires_in": 3600
        }
        
        with patch('app.services.oauth_service.oauth_provider_manager') as mock_manager:
            mock_config = Mock()
            mock_config.client_id = "test_client_id"
            mock_config.client_secret = "test_client_secret"
            mock_config.redirect_uri = "http://localhost:8000/callback"
            mock_config.token_url = "https://oauth2.googleapis.com/token"
            
            mock_manager.get_provider_config.return_value = mock_config
            
            with patch('httpx.AsyncClient') as mock_client:
                mock_client.return_value.__aenter__.return_value.post = AsyncMock(return_value=mock_response)
                
                tokens, state_data = await oauth_service.exchange_code_for_tokens(code, state)
                
                # Assertions
                assert tokens["access_token"] == "test_access_token"
                assert tokens["refresh_token"] == "test_refresh_token"
                assert state_data["user_id"] == "test_user"
                assert state_data["provider"] == "google"
                
                # Verify Redis cleanup
                mock_redis_service.delete_data_from_redis.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_exchange_code_for_tokens_invalid_state(self, oauth_service, mock_redis_service):
        """Test token exchange with invalid state."""
        mock_redis_service.get_json_data_from_redis.return_value = None
        
        with pytest.raises(ValueError, match="Invalid or expired OAuth state"):
            await oauth_service.exchange_code_for_tokens("code", "invalid_state")
    
    def test_store_oauth_credentials_new(self, oauth_service, test_db, mock_secret_manager):
        """Test storing new OAuth credentials."""
        user_id = "test_user"
        mcp_id = "test_mcp"
        tool_name = "google_calendar"
        provider = OAuthProvider.GOOGLE
        tokens = {"access_token": "test_token"}
        scopes = ["https://www.googleapis.com/auth/calendar"]
        
        result = oauth_service.store_oauth_credentials(
            test_db, user_id, mcp_id, tool_name, provider, tokens, scopes
        )
        
        # Assertions
        assert result["success"] is True
        assert "stored successfully" in result["message"]
        
        # Verify database record
        credential = test_db.query(OAuthCredential).first()
        assert credential is not None
        assert credential.user_id == user_id
        assert credential.provider == provider.value
        
        # Verify Secret Manager was called
        mock_secret_manager.store_oauth_tokens.assert_called_once()
    
    def test_store_oauth_credentials_update_existing(self, oauth_service, test_db, mock_secret_manager):
        """Test updating existing OAuth credentials."""
        # Create existing credential
        existing_credential = OAuthCredential(
            user_id="test_user",
            mcp_id="test_mcp",
            tool_name="google_calendar",
            provider="google",
            composite_key="test_user_test_mcp_google_calendar_google",
            secret_reference="old_secret_id",
            scopes=json.dumps(["old_scope"])
        )
        test_db.add(existing_credential)
        test_db.commit()
        
        # Update credentials
        tokens = {"access_token": "new_token"}
        scopes = ["new_scope"]
        
        result = oauth_service.store_oauth_credentials(
            test_db, "test_user", "test_mcp", "google_calendar", 
            OAuthProvider.GOOGLE, tokens, scopes
        )
        
        # Assertions
        assert result["success"] is True
        
        # Verify update
        updated_credential = test_db.query(OAuthCredential).first()
        assert updated_credential.secret_reference == "test_secret_id"
        assert json.loads(updated_credential.scopes) == scopes
    
    def test_retrieve_oauth_credentials_success(self, oauth_service, test_db, mock_secret_manager):
        """Test successful credential retrieval."""
        # Create test credential
        credential = OAuthCredential(
            user_id="test_user",
            mcp_id="test_mcp",
            tool_name="google_calendar",
            provider="google",
            composite_key="test_user_test_mcp_google_calendar_google",
            secret_reference="test_secret_id",
            scopes=json.dumps(["https://www.googleapis.com/auth/calendar"])
        )
        test_db.add(credential)
        test_db.commit()
        
        # Retrieve credentials
        result = oauth_service.retrieve_oauth_credentials(
            test_db, "test_user", "test_mcp", "google_calendar", OAuthProvider.GOOGLE
        )
        
        # Assertions
        assert result is not None
        assert result["access_token"] == "test_access_token"
        assert result["user_id"] == "test_user"
        assert result["provider"] == "google"
        
        # Verify Secret Manager was called
        mock_secret_manager.retrieve_oauth_tokens.assert_called_once()
    
    def test_retrieve_oauth_credentials_not_found(self, oauth_service, test_db):
        """Test credential retrieval when not found."""
        result = oauth_service.retrieve_oauth_credentials(
            test_db, "nonexistent_user", "mcp", "tool", OAuthProvider.GOOGLE
        )
        
        assert result is None
    
    def test_delete_oauth_credentials_success(self, oauth_service, test_db, mock_secret_manager):
        """Test successful credential deletion."""
        # Create test credential
        credential = OAuthCredential(
            user_id="test_user",
            mcp_id="test_mcp",
            tool_name="google_calendar",
            provider="google",
            composite_key="test_user_test_mcp_google_calendar_google",
            secret_reference="test_secret_id",
            scopes=json.dumps(["scope"])
        )
        test_db.add(credential)
        test_db.commit()
        
        # Delete credentials
        result = oauth_service.delete_oauth_credentials(
            test_db, "test_user", "test_mcp", "google_calendar", OAuthProvider.GOOGLE
        )
        
        # Assertions
        assert result is True
        
        # Verify deletion from database
        deleted_credential = test_db.query(OAuthCredential).first()
        assert deleted_credential is None
        
        # Verify Secret Manager deletion
        mock_secret_manager.delete_oauth_tokens.assert_called_once()
    
    def test_delete_oauth_credentials_not_found(self, oauth_service, test_db):
        """Test credential deletion when not found."""
        result = oauth_service.delete_oauth_credentials(
            test_db, "nonexistent_user", "mcp", "tool", OAuthProvider.GOOGLE
        )
        
        # Should return True even if not found
        assert result is True
