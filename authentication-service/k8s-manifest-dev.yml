apiVersion: v1
kind: ServiceAccount
metadata:
  name: authentication-service-ai-sa
  namespace: ruh-dev
  labels:
    name: authentication-service-ai-sa
    namespace: ruh-dev
    app: authentication-service-ai
    deployment: authentication-service-ai-dp
---
# Create Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: authentication-service-ai-dp
  namespace: ruh-dev
  labels:
    name: authentication-service-ai-dp
    namespace: ruh-dev
    app: authentication-service-ai
    serviceaccount: authentication-service-ai-sa
    deployment: authentication-service-ai-dp
spec:
  replicas: 1
  selector:
    matchLabels:
      app: authentication-service-ai
      deployment: authentication-service-ai-dp
  template:
    metadata:
      labels:
        namespace: ruh-dev
        app: authentication-service-ai
        deployment: authentication-service-ai-dp
    spec:
      serviceAccountName: authentication-service-ai-sa      
      containers:
      - name: authentication-service-ai
        image: us-central1-docker.pkg.dev/<PROJECT_ID>/<REPOSITORY>/<IMAGE_NAME>:<ENV>-<VERSION>
        resources:
          requests:
            memory: 64Mi
            cpu: 50m
          limits:
            memory: 1024Mi
            cpu: 250m
        ports:
        - containerPort: 50059
      #   readinessProbe:
      #     tcpSocket:
      #       port: 5001
      #     initialDelaySeconds: 5
      #     periodSeconds: 10
      #   livenessProbe:
      #     tcpSocket:
      #       port: 5001
      #     initialDelaySeconds: 15
      #     periodSeconds: 20
      # tolerations:
      # - key: "spotInstance"
      #   operator: "Equal"
      #   value: "true"
      #   effect: "PreferNoSchedule"
      # nodeSelector:    
      #   eks.amazonaws.com/capacityType: SPOT       
---
#### Create Service
apiVersion: v1
kind: Service
metadata:
  name: authentication-service-ai-svc
  namespace: ruh-dev
spec:
  selector:
    app: authentication-service-ai
    deployment: authentication-service-ai-dp
  ports:
    - protocol: TCP
      port: 80
      targetPort: 50059
  sessionAffinityConfig:
    clientIP:
      timeoutSeconds: 100000
---
### Create HPA
# apiVersion: autoscaling/v2beta1
# kind: HorizontalPodAutoscaler
# metadata:
#   name:authentication-service-user-hpa
#   namespace: ruh-dev
# spec:
#   scaleTargetRef:
#     apiVersion: apps/v1
#     kind: Deployment
#     name:authentication-service-user-dp
#   minReplicas: 1
#   maxReplicas: 2
#   metrics:
#     - type: Resource
#       resource:
#         name: cpu
#         targetAverageUtilization: 60
---
### Create Nginx Ingress
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: authentication-service-user-ingress
  namespace: ruh-dev
spec:
  ingressClassName: nginx
  rules:
  - host: authentication-api-ruh-dev.rapidinnovation.dev
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: authentication-service-ai-svc
            port:
              number: 80
